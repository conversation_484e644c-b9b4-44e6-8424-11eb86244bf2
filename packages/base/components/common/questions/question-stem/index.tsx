import { computed, defineComponent } from 'vue'
import type { PropType } from 'vue'
import type { TransformToVoQuestionData } from '@sa/utils'
import styles from './index.module.css'

export default defineComponent({
  name: 'QuestionStem',
  props: {
    /**
     * 题目数据
     */
    item: {
      type: Object as PropType<TransformToVoQuestionData>,
      required: true,
    },
    /**
     * 题目序号
     */
    questionNumber: {
      type: Number,
      default: 1,
    },
    /**
     * 是否显示题目序号
     */
    showNumber: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否显示题型标签
     */
    showType: {
      type: Boolean,
      default: true,
    },

    /**
     * 自定义CSS类名
     */
    className: {
      type: String,
      default: '',
    },
  },
  emits: ['image-click'],
  setup(props, { emit }) {
    // 计算题目标题，支持HTML内容
    const questionTitle = computed(() => {
      return props.item.title || ''
    })

    // 计算题型显示文本
    const typeText = computed(() => {
      return props.item.typeText || '题目'
    })

    // 计算容器样式类
    const containerClass = computed(() => {
      const baseClass = styles.questionStem
      const customClass = props.className

      return [baseClass, customClass].filter(Boolean).join(' ')
    })

    // 处理图片点击事件
    const handleImageClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (target.tagName === 'IMG') {
        const imgSrc = (target as HTMLImageElement).src
        emit('image-click', imgSrc)
      }
    }

    return () => {
      return (
        <div class={containerClass.value} onClick={handleImageClick}>
          {/* 题目头部信息 */}
          {(props.showNumber || props.showType) && (
            <div class={styles.questionHeader}>
              {props.showNumber && (
                <span class={styles.questionNumber}>
                  第
                  {props.questionNumber}
                  题
                </span>
              )}
              {props.showType && (
                <span class={styles.questionType}>
                  {typeText.value}
                </span>
              )}
            </div>
          )}

          {/* 题目内容 */}
          {questionTitle.value}
          <div
            class={styles.questionContent}
            v-html={questionTitle.value}
            v-katex
          />
        </div>
      )
    }
  },
})
